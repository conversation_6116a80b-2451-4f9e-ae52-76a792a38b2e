/* eslint-disable @typescript-eslint/no-explicit-any */

import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, Link } from "react-router-dom";
import type { AppDispatch, RootState } from "../../app/store";
import {
  signupSuccess,
  signupFailure,
  setLoading,
  clearAuthError,
} from "../../features/auth/authSlice";
import loginImg from "../../assets/login-left.png";
import logo from "../../assets/Flashana Logo.png";
import { Button } from "@/components/ui/button";
import { usePostSignup } from "./hooks";
import toastNotification from "../../utility/toastService/toastNotification";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const SignUpPage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const { isLoading, error: authError } = useSelector(
    (state: RootState) => state.auth
  );

  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [email, setEmail] = useState("");
  const [companyName, setCompanyName] = useState("");
  const [companyType, setCompanyType] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [agreed, setAgreed] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const onPostSuccess = (data: any) => {
    console.log("Signup success:", data);
    // toastNotification.success(data?.message || "Signup successful");

    // Store user data in redux store
    const userInfo = {
      email: email || "",
      user_id: data?.data?.user_id || "",
      tenant_id: data?.data?.tenant_id || "",
    };
    dispatch(signupSuccess(userInfo));
    navigate("/otp-verification");
    // setTimeout(() => {
    //   navigate("/otp-verification");
    // }, 3000);
  };

  const onPostError = (error: any) => {
    dispatch(setLoading(false));
    const errorInfo =
      error?.error?.message || "Something went wrong. Please try again.";
    toastNotification.error(errorInfo);
  };

  const { postMutate } = usePostSignup(onPostSuccess, onPostError);

  useEffect(() => {
    dispatch(clearAuthError());
  }, [dispatch]);

  const handleSignUpSubmit = async (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    dispatch(clearAuthError());

    const newErrors: { [key: string]: string } = {};

    if (!firstName) newErrors.firstName = "First name is required.";
    if (!lastName) newErrors.lastName = "Last name is required.";
    if (!email) newErrors.email = "Email is required.";
    if (!phoneNumber) newErrors.phoneNumber = "Phone number is required.";
    if (!companyName) newErrors.companyName = "Company name is required.";
    if (!companyType) newErrors.companyType = "Company type is required.";
    if (!password) newErrors.password = "Password is required.";
    if (!confirmPassword)
      newErrors.confirmPassword = "Please confirm your password.";
    if (password && confirmPassword && password !== confirmPassword)
      newErrors.confirmPassword = "Passwords do not match.";
    if (!agreed)
      newErrors.agreed = "You must accept the Terms and Privacy Policy.";

    setErrors(newErrors);

    if (Object.keys(newErrors).length > 0) {
      return; // Don't submit
    }

    dispatch(setLoading(true));

    // Use React Query mutation instead of direct service call
    const userDataPayload: any = {
      email: email,
      password: password,
      first_name: firstName,
      last_name: lastName,
      phone_country_code: "91",
      phone_number: phoneNumber,
      country: "India",
      tenant_name: companyName,
      industry_id: 1,
    };
    postMutate(userDataPayload);
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 h-screen min-h-0">
      <div className="hidden md:flex items-center justify-center h-screen">
        <img
          src={loginImg}
          alt="Signup"
          className="w-full h-full object-cover"
        />
      </div>
      <div className="flex items-center justify-center bg-white px-6 h-screen min-h-0 overflow-y-auto">
        <div className="w-full max-w-md space-y-6 pt-[180px] pb-[48px]">
          <div className="text-center">
            <img src={logo} alt="Flashana Logo" className="mx-auto w-[200px]" />
            <h2 className="text-3xl font-bold text-[#22423F] mt-6">Signup</h2>
            <p className="text-sm text-gray-600 mt-2">
              Create your account to get started.
            </p>
          </div>
          {/* <form onSubmit={handleSignUpSubmit} className="space-y-5"> */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="firstName" className="mb-1">
                First Name<span className="text-red-500">*</span>
              </Label>
              <Input
                id="firstName"
                type="text"
                value={firstName}
                onChange={(e) => {
                  setFirstName(e.target.value);
                  if (errors.firstName)
                    setErrors((prev) => ({ ...prev, firstName: "" }));
                }}
                required
              />
              {errors.firstName && (
                <p className="text-sm text-red-600">{errors.firstName}</p>
              )}
            </div>
            <div>
              <Label htmlFor="lastName" className="mb-1">
                Last Name<span className="text-red-500">*</span>
              </Label>
              <Input
                id="lastName"
                type="text"
                value={lastName}
                onChange={(e) => {
                  setLastName(e.target.value);
                  if (errors.lastName)
                    setErrors((prev) => ({ ...prev, lastName: "" }));
                }}
                required
              />
              {errors.lastName && (
                <p className="text-sm text-red-600">{errors.lastName}</p>
              )}
            </div>
          </div>

          <div>
            <Label htmlFor="phoneNumber" className="mb-1">
              Phone Number<span className="text-red-500">*</span>
            </Label>
            <Input
              id="phoneNumber"
              type="tel"
              value={phoneNumber}
              onChange={(e) => {
                setPhoneNumber(e.target.value);
                if (errors.phoneNumber)
                  setErrors((prev) => ({ ...prev, phoneNumber: "" }));
              }}
              required
            />
            {errors.phoneNumber && (
              <p className="text-sm text-red-600">{errors.phoneNumber}</p>
            )}
          </div>

          <div>
            <Label htmlFor="email" className="mb-1">
              Email<span className="text-red-500">*</span>
            </Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => {
                setEmail(e.target.value);
                dispatch(clearAuthError());
                if (errors.email) setErrors((prev) => ({ ...prev, email: "" }));
              }}
              required
            />
            {errors.email && (
              <p className="text-sm text-red-600">{errors.email}</p>
            )}
          </div>

          <div>
            <Label htmlFor="companyName" className="mb-1">
              Company Name<span className="text-red-500">*</span>
            </Label>
            <Input
              id="companyName"
              type="text"
              value={companyName}
              onChange={(e) => {
                setCompanyName(e.target.value);
                if (errors.companyName)
                  setErrors((prev) => ({ ...prev, companyName: "" }));
              }}
              required
            />
            {errors.companyName && (
              <p className="text-sm text-red-600">{errors.companyName}</p>
            )}
          </div>

          <div>
            <Label
              htmlFor="companyType"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Company Type<span className="text-red-500">*</span>
            </Label>
            <Select
              value={companyType}
              onValueChange={(value) => {
                setCompanyType(value);
                if (errors.companyType) {
                  setErrors((prev) => ({ ...prev, companyType: "" }));
                }
              }}
              required
            >
              <SelectTrigger id="companyType" className="w-full">
                <SelectValue placeholder="Select" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Retail">Retail</SelectItem>
                <SelectItem value="Service">Service</SelectItem>
                <SelectItem value="Tech">Tech</SelectItem>
                <SelectItem value="Other">Other</SelectItem>
              </SelectContent>
            </Select>
            {errors.companyType && (
              <p className="text-sm text-red-600">{errors.companyType}</p>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="password" className="mb-1">
                Password<span className="text-red-500">*</span>
              </Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => {
                  setPassword(e.target.value);
                  dispatch(clearAuthError());
                  if (errors.password)
                    setErrors((prev) => ({ ...prev, password: "" }));
                }}
                required
              />
              {errors.password && (
                <p className="text-sm text-red-600">{errors.password}</p>
              )}
            </div>
            <div>
              <Label htmlFor="confirmPassword" className="mb-1">
                Confirm Password<span className="text-red-500">*</span>
              </Label>
              <Input
                id="confirmPassword"
                type="password"
                value={confirmPassword}
                onChange={(e) => {
                  setConfirmPassword(e.target.value);
                  dispatch(clearAuthError());
                  if (errors.confirmPassword)
                    setErrors((prev) => ({ ...prev, confirmPassword: "" }));
                }}
                required
              />
              {errors.confirmPassword && (
                <p className="text-sm text-red-600">{errors.confirmPassword}</p>
              )}
            </div>
          </div>

          <div className="flex items-start space-x-2">
            <input
              type="checkbox"
              checked={agreed}
              onChange={(e) => {
                setAgreed(e.target.checked);
                if (errors.agreed)
                  setErrors((prev) => ({ ...prev, agreed: "" }));
              }}
              className="mt-1"
              required
            />
            <label className="text-sm text-gray-600">
              By signing up you agree to our{" "}
              <a href="/terms" className="text-sm text-secondary font-medium">
                Terms of Service
              </a>{" "}
              and{" "}
              <a href="/privacy" className="text-sm text-secondary font-medium">
                Privacy Policy
              </a>
              .
            </label>
          </div>

          {authError && (
            <p className="text-sm text-red-600 text-center">{authError}</p>
          )}

          <Button
            type="submit"
            onClick={handleSignUpSubmit}
            className="w-full py-2 bg-[#22423F] text-white font-semibold"
          >
            {isLoading ? "Signing Up..." : "Sign Up"}
          </Button>

          <p className="text-sm text-center text-gray-600">
            Already have an account?{" "}
            <Link to="/login" className="text-sm text-secondary font-medium">
              Log In
            </Link>
          </p>
          {/* </form> */}
        </div>
      </div>
    </div>
  );
};

export default SignUpPage;
