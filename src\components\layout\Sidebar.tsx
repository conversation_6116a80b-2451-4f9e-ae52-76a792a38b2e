import React, { useState } from "react";
import { NavLink } from "react-router-dom";
import {
  HomeIcon,
  WrenchScrewdriverIcon,
  CloudArrowUpIcon,
  TableCellsIcon,
  PresentationChartLineIcon,
  BuildingOffice2Icon,
  CalendarDaysIcon,
  MapPinIcon,
} from "@heroicons/react/24/outline";
import logo from "../../assets/Flashana Logo.png";

interface NavItem {
  name: string;
  path: string;
  icon: React.ForwardRefExoticComponent<
    React.PropsWithoutRef<React.SVGProps<SVGSVGElement>> & {
      title?: string;
      titleId?: string;
    } & React.RefAttributes<SVGSVGElement>
  >;
}

const navItems: NavItem[] = [
  { name: "Dashboard", path: "/dashboard", icon: HomeIcon },
  { name: "Company Info", path: "/company-info", icon: BuildingOffice2Icon },
  {
    name: "Company Details",
    path: "/company-details",
    icon: WrenchScrewdriverIcon,
  },
  { name: "Holiday List", path: "/holiday-list", icon: CalendarDaysIcon },
  { name: "Stores", path: "/stores", icon: MapPinIcon },
  { name: "Data Ingestion", path: "/data-ingestion", icon: CloudArrowUpIcon },
  {
    name: "Upload Sales Data",
    path: "/upload-sales-data",
    icon: CloudArrowUpIcon,
  },
  { name: "Imported Data", path: "/imported-data", icon: TableCellsIcon },
  {
    name: "Forecasting",
    path: "/forecasting",
    icon: PresentationChartLineIcon,
  },
];

const Sidebar: React.FC = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);

  const activeLinkClasses = `bg-flashanaGreen text-white`;
  const inactiveLinkClasses = `text-gray-300 hover:bg-gray-700 hover:text-white`;
  const activeIconClasses = `text-white`;
  const inactiveIconClasses = `text-gray-400 group-hover:text-gray-200`;

  return (
    <div
      className={`bg-gray-800 text-white h-screen flex flex-col transition-width duration-300 ease-in-out ${
        isSidebarOpen ? "w-64" : "w-16"
      }`}
    >
      <div
        className={`flex items-center border-b border-gray-700 shrink-0 h-16
  ${isSidebarOpen ? "justify-between p-4" : "justify-center p-0"}`}
      >
        {isSidebarOpen && (
          <img
            src={logo}
            alt="Flashana Logo"
            className="h-8"
            style={{ objectFit: "contain" }}
          />
        )}
        <button
          onClick={() => setIsSidebarOpen(!isSidebarOpen)}
          className="p-1.5 bg-flashanaGreen rounded-md text-gray-300 hover:bg-gray-700 focus:outline-none"
          aria-label={isSidebarOpen ? "Close sidebar" : "Open sidebar"}
        >
          {isSidebarOpen ? (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              strokeWidth="2"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          ) : (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              strokeWidth="2"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M4 6h16M4 12h16M4 18h16"
              />
            </svg>
          )}
        </button>
      </div>

      <nav className="flex-grow p-2 space-y-1 overflow-y-auto">
        {navItems.map((item) => (
          <NavLink
            key={item.name}
            to={item.path}
            title={!isSidebarOpen ? item.name : undefined}
            className={({ isActive }) =>
              `w-full flex items-center p-2.5 rounded-md transition-colors duration-150 group
               ${isActive ? activeLinkClasses : inactiveLinkClasses}
               ${!isSidebarOpen ? "justify-center !px-0" : "px-4"}`
            }
          >
            <item.icon
              className={`h-6 w-6 shrink-0 ${isSidebarOpen ? "mr-3" : ""}
                         ${({ isActive }: { isActive: boolean }) =>
                           isActive ? activeIconClasses : inactiveIconClasses}`}
            />
            {isSidebarOpen && (
              <span className="whitespace-nowrap">{item.name}</span>
            )}
          </NavLink>
        ))}
      </nav>
    </div>
  );
};

export default Sidebar;
