/* eslint-disable @typescript-eslint/no-explicit-any */

import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, Link } from "react-router-dom";
import type { AppDispatch, RootState } from "../../app/store";
import loginImg from "../../assets/login-left.png";
import logo from "../../assets/Flashana Logo.png";
import {
  loginSuccess,
  setLoading,
  clearAuthError,
} from "../../features/auth/authSlice";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Eye, EyeOff } from "lucide-react";
import { usePostSignin } from "./hooks";
import toastNotification from "../../utility/toastService/toastNotification";

const LoginPage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const { error: authError, isLoading } = useSelector(
    (state: RootState) => state.auth
  );

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  // Use the login mutation from our custom hook
  // const { loginMutation } = useAuth();

  useEffect(() => {
    dispatch(clearAuthError());
  }, [dispatch]);

  const onPostSuccess = (data: any) => {
    console.log("Signin success:", data);
    toastNotification.success(data?.message || "Signin successful");

    const responseData = data?.result?.data;

    const userInfo = {
      username: responseData?.user?.email || "", // or any other unique username
      email: responseData?.user?.email || "",
      user_id: responseData?.user?.user_id || "",
      tenant_id: responseData?.user?.tenant_id || "",
      access_token: responseData?.access_token || "",
      full_name: `${responseData?.user?.first_name || ""} ${
        responseData?.user?.last_name || ""
      }`.trim(),
      role: responseData?.roles?.[0] || "",
      isAuthenticated: true,
    };

    dispatch(loginSuccess(userInfo));

    setTimeout(() => {
      navigate("/dashboard");
    }, 2000);
  };

  const onPostError = (error: any) => {
    dispatch(setLoading(false));
    const errorInfo =
      error?.error?.message || "Something went wrong. Please try again.";
    toastNotification.error(errorInfo);
  };

  const { postMutate } = usePostSignin(onPostSuccess, onPostError);

  useEffect(() => {
    dispatch(clearAuthError());
  }, [dispatch]);

  const handleSubmit = async (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    dispatch(clearAuthError());

    const newErrors: { [key: string]: string } = {};

    if (!email) newErrors.email = "Email is required.";
    if (!password) newErrors.password = "Password is required.";

    setErrors(newErrors);

    if (Object.keys(newErrors).length > 0) {
      return; // Don't submit
    }

    dispatch(setLoading(true));

    // Use React Query mutation instead of direct service call
    const userDataPayload: any = {
      email: email,
      password: password,
    };
    postMutate(userDataPayload);
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 h-screen">
      <div className="w-full h-full overflow-hidden flex items-center justify-center">
        <img src={loginImg} alt="Login" className="w-full h-full object-fill" />
      </div>
      <div className="flex items-center justify-center flex-col bg-white">
        <div className="w-full max-w-md space-y-6 pt-[180px] pb-[48px]">
          <div className="flex justify-center flex-col gap-[40px]">
            <div className="flex justify-center">
              <img src={logo} alt="Login" className="w-[220px]" />
            </div>
            <div>
              <h2 className="text-3xl text-primary text-center font-semibold mb-4">
                Login
              </h2>
              <p className="text-center">
                Access your account by logging in below
              </p>
            </div>
          </div>
          {/* <form onSubmit={handleSubmit} className="space-y-6"> */}
          <div>
            <Label
              htmlFor="email-login"
              className="block text-sm font-medium text-gray-700"
            >
              Email<span className="text-red-500">*</span>
            </Label>
            <Input
              id="email-login"
              name="email"
              type="email"
              autoComplete="email"
              required
              value={email}
              onChange={(e) => {
                setEmail(e.target.value);
                dispatch(clearAuthError());
                if (errors.email) setErrors((prev) => ({ ...prev, email: "" }));
              }}
            />
            {errors.email && (
              <p className="text-sm text-red-600">{errors.email}</p>
            )}
          </div>
          <div>
            <div className="flex justify-between items-center">
              <Label
                htmlFor="password-login"
                className="block text-sm font-medium text-gray-700"
              >
                Password<span className="text-red-500">*</span>
              </Label>
            </div>
            <div className="relative">
              <Input
                id="password-login"
                name="password"
                type={showPassword ? "text" : "password"}
                autoComplete="current-password"
                required
                value={password}
                onChange={(e) => {
                  setPassword(e.target.value);
                  dispatch(clearAuthError());
                  if (errors.password)
                    setErrors((prev) => ({ ...prev, password: "" }));
                }}
                className="pr-10"
              />
              {errors.password && (
                <p className="text-sm text-red-600">{errors.password}</p>
              )}
              <button
                type="button"
                className="absolute inset-y-0 right-0 flex items-center px-3 text-gray-400"
                tabIndex={-1}
                onClick={() => setShowPassword((v) => !v)}
              >
                {showPassword ? (
                  <EyeOff className="w-5 h-5" />
                ) : (
                  <Eye className="w-5 h-5" />
                )}
              </button>
            </div>
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <input
                id="remember-me"
                name="remember-me"
                type="checkbox"
                checked={rememberMe}
                onChange={(e) => setRememberMe(e.target.checked)}
                className="h-4 w-4 text-primary border-gray-300 rounded"
              />
              <label
                htmlFor="remember-me"
                className="ml-2 block text-sm text-gray-900"
              >
                Remember me
              </label>
            </div>
            <div className="flex justify-between items-center">
              <Link
                to="/forgot-password"
                className="text-sm text-secondary font-medium"
              >
                Forgot password?
              </Link>
            </div>
          </div>
          {/* Google reCAPTCHA placeholder */}
          <div>
            {/* Replace this div with your actual reCAPTCHA component if needed */}
            <div className="bg-gray-50 border border-gray-200 rounded p-3 flex items-center gap-2">
              <input type="checkbox" className="h-4 w-4" disabled />
              <span className="text-sm text-gray-500">I'm not a robot</span>
              <img
                src="https://www.gstatic.com/recaptcha/api2/logo_48.png"
                alt="reCAPTCHA"
                className="h-6 ml-auto"
              />
            </div>
          </div>
          {authError && (
            <p className="text-sm text-red-600 text-center">{authError}</p>
          )}
          <Button
            type="submit"
            onClick={handleSubmit}
            className="bg-primary text-white w-full py-2.5 font-semibold"
          >
            {isLoading ? "Logging In..." : "Log In"}
          </Button>
          <p className="text-sm text-center text-gray-600">
            Don't have an account?{" "}
            <Link to="/signup" className="text-secondary font-medium">
              Sign Up
            </Link>
          </p>
          {/* </form> */}
          <p className="text-xs text-center text-gray-500 mt-4">
            By logging in you agree to our{" "}
            <a href="/terms" className="text-secondary font-medium">
              Terms of Service
            </a>{" "}
            and{" "}
            <a href="/privacy" className="text-secondary font-medium">
              Privacy Policy
            </a>
            .
          </p>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
