import { createSlice } from "@reduxjs/toolkit";
import type { PayloadAction } from "@reduxjs/toolkit";

interface AppUser {
  username: string;
  email?: string;
}

interface SignUpUser {
  email?: string;
  user_id: number;
  tenant_id: string;
}

interface ForgotPwdUser {
  email?: string;
}

interface AuthState {
  isAuthenticated: boolean;
  user: AppUser | null;
  signupUser: SignUpUser | null;
  forgotPwdUser: ForgotPwdUser | null;
  isLoading: boolean;
  error: string | null;
  requiresConfirmation?: boolean;
  confirmationUsername?: string | null;
}

const initialState: AuthState = {
  isAuthenticated: false,
  user: null,
  signupUser: null,
  forgotPwdUser: null,
  isLoading: true, // Start with loading true to check auth state on app init
  error: null,
  requiresConfirmation: false,
  confirmationUsername: null,
};

export const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    loginSuccess: (state, action: PayloadAction<AppUser>) => {
      state.isAuthenticated = true;
      state.user = action.payload;
      state.isLoading = false;
      state.error = null;
      state.requiresConfirmation = false;
      state.confirmationUsername = null;
    },
    loginFailure: (state, action: PayloadAction<string>) => {
      state.isAuthenticated = false;
      state.user = null;
      state.isLoading = false;
      state.error = action.payload;
    },
    logoutSuccess: (state) => {
      state.isAuthenticated = false;
      state.user = null;
      state.isLoading = false;
      state.error = null;
    },
    signupRequiresConfirmation: (state, action: PayloadAction<string>) => {
      state.requiresConfirmation = true;
      state.confirmationUsername = action.payload;
      state.isLoading = false;
      state.error = null;
    },
    signupSuccess: (state, action: PayloadAction<SignUpUser>) => {
      state.signupUser = action.payload;
      state.requiresConfirmation = false;
      state.confirmationUsername = null;
      state.isLoading = false;
      state.error = null;
    },
    signupFailure: (state, action: PayloadAction<string>) => {
      state.isLoading = false;
      state.error = action.payload;
    },
    clearAuthError: (state) => {
      state.error = null;
    },
    otpVerifyFailure: (state, action: PayloadAction<string>) => {
      state.isLoading = false;
      state.error = action.payload;
    },
    forgotPasswordFailure: (state, action: PayloadAction<string>) => {
      state.isLoading = false;
      state.error = action.payload;
    },
    forgotPasswordSuccess: (state, action: PayloadAction<ForgotPwdUser>) => {
      state.forgotPwdUser = action.payload;
      state.isLoading = false;
      state.error = null;
    },
  },
});

export const {
  setLoading,
  loginSuccess,
  loginFailure,
  logoutSuccess,
  signupRequiresConfirmation,
  signupSuccess,
  signupFailure,
  clearAuthError,
  otpVerifyFailure,
  forgotPasswordFailure,
  forgotPasswordSuccess,
} = authSlice.actions;
export default authSlice.reducer;
