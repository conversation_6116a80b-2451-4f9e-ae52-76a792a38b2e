{"name": "flashana-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@reduxjs/toolkit": "^2.8.2", "@tanstack/react-query": "^5.80.7", "@tanstack/react-query-devtools": "^5.80.10", "aws-amplify": "^6.15.0", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "env-cmd": "^10.1.0", "input-otp": "^1.4.2", "lucide-react": "^0.516.0", "papaparse": "^5.5.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.58.1", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.2", "react-toastify": "^11.0.5", "redux": "^5.0.1", "tailwind-merge": "^3.3.1", "tailwind-variants": "^1.0.0", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/node": "^24.0.3", "@types/papaparse": "^5.3.16", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/react-redux": "^7.1.34", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.1", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}