// import React, { useEffect } from "react";
// import {
//   BrowserRouter as Router,
//   Routes,
//   Route,
//   Navigate,
//   useLocation,
// } from "react-router-dom";
// import { useSelector, useDispatch } from "react-redux";
// import type { RootState, AppDispatch } from "./app/store";
// import { getCurrentUser, fetchUserAttributes } from "aws-amplify/auth";
// import {
//   loginSuccess,
//   logoutSuccess,
//   setLoading,
// } from "./features/auth/authSlice";
// import AppShell from "./components/layout/AppShell";
// import AuthLayout from "./components/layout/AuthLayout";
// import WizardLayout from "./components/layout/WizardLayout";
// import LoginPage from "./components/auth/LoginPage";
// import SignUpPage from "./components/auth/SignUpPage";

// const DashboardPage: React.FC = () => (
//   <div className="text-2xl font-bold">Dashboard Content (TO-DO!!!)</div>
// );
// const CompanyInfoPage: React.FC = () => (
//   <div className="text-2xl font-bold">Company Info Content (TO-DO!!!)</div>
// );
// const CompanyDetailsPage: React.FC = () => (
//   <div className="text-2xl font-bold">Company Details Content (TO-DO!!!)</div>
// );
// const HolidayListPage: React.FC = () => (
//   <div className="text-2xl font-bold">Holiday List Content (TO-DO!!!)</div>
// );
// const StoresPage: React.FC = () => (
//   <div className="text-2xl font-bold">Stores Content (TO-DO!!!)</div>
// );
// const DataIngestionPage: React.FC = () => (
//   <div className="text-2xl font-bold">Data Ingestion Content (TO-DO!!!)</div>
// );
// const ImportedDataPage: React.FC = () => (
//   <div className="text-2xl font-bold">Imported Data Content (TO-DO!!!)</div>
// );
// const ForecastingPage: React.FC = () => (
//   <div className="text-2xl font-bold">Forecasting Content (TO-DO!!!)</div>
// );
// const NotFoundPage: React.FC = () => (
//   <div className="text-2xl font-bold text-center mt-10">
//     404 - Page Not Found
//   </div>
// );

// interface ProtectedRouteProps {
//   children: React.ReactNode;
// }

// const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
//   const isAuthenticated = useSelector(
//     (state: RootState) => state.auth.isAuthenticated
//   );
//   const isLoadingAuth = useSelector((state: RootState) => state.auth.isLoading);
//   const location = useLocation();

//   if (isLoadingAuth) {
//     return (
//       <div className="min-h-screen flex items-center justify-center bg-gray-100">
//         <span className="flex flex-col items-center">
//           <p className="text-xl font-semibold text-gray-700">Loading...</p>
//         </span>
//       </div>
//     );
//   }

//   if (!isAuthenticated) {
//     return <Navigate to="/login" state={{ from: location }} replace />;
//   }
//   return <>{children}</>;
// };

// function App() {
//   const dispatch = useDispatch<AppDispatch>();

//   useEffect(() => {
//     const checkAuthState = async () => {
//       try {
//         const cognitoUser = await getCurrentUser();
//         const attributes = await fetchUserAttributes();
//         const appUsername = attributes.email || cognitoUser.username;
//         dispatch(
//           loginSuccess({
//             username: appUsername,
//             email: attributes.email,
//           })
//         );
//       } catch (error) {
//         dispatch(logoutSuccess());
//       } finally {
//         dispatch(setLoading(false));
//       }
//     };

//     checkAuthState();
//   }, [dispatch]);

//   return (
//     <Router>
//       <Routes>
//         <Route
//           path="/login"
//           element={
//             <AuthLayout>
//               <LoginPage />
//             </AuthLayout>
//           }
//         />
//         <Route
//           path="/signup"
//           element={
//             <AuthLayout>
//               <SignUpPage />
//             </AuthLayout>
//           }
//         />

//         <Route
//           path="/"
//           element={
//             <ProtectedRoute>
//               <AppShell />
//             </ProtectedRoute>
//           }
//         >
//           <Route index element={<Navigate replace to="dashboard" />} />
//           <Route path="dashboard" element={<DashboardPage />} />
//           <Route path="company-info" element={<CompanyInfoPage />} />
//           <Route path="company-details" element={<CompanyDetailsPage />} />
//           <Route path="holiday-list" element={<HolidayListPage />} />
//           <Route path="stores" element={<StoresPage />} />
//           <Route path="data-ingestion" element={<DataIngestionPage />} />
//           <Route path="upload-sales-data" element={<WizardLayout />} />
//           <Route path="imported-data" element={<ImportedDataPage />} />
//           <Route path="forecasting" element={<ForecastingPage />} />
//           <Route path="*" element={<NotFoundPage />} />
//         </Route>
//       </Routes>
//     </Router>
//   );
// }
// export default App;

import React from "react";
import { Provider } from "react-redux";
import { store } from "./app/store";
import Routers from "../src/routers/routers";
import AuthInitializer from "./components/auth/AuthInitializer";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";

// Create a client
// const queryClient = new QueryClient({
//   defaultOptions: {
//     queries: {
//       staleTime: 5 * 60 * 1000, // 5 minutes
//       retry: 1,
//       refetchOnWindowFocus: false,
//     },
//   },
//   queryCache: new QueryCache({
//     onError: (error) => {
//       console.error(`Something went wrong: ${error.message}`);
//     },
//   }),
//   mutationCache: new MutationCache({
//     onError: (error) => {
//       console.error(`Mutation error: ${error.message}`);
//     },
//   }),
// });
const queryClient = new QueryClient();

const App = () => {
  return (
    <Provider store={store}>
      <QueryClientProvider client={queryClient}>
        <AuthInitializer>
          <ToastContainer
            position="top-right"
            autoClose={5000}
            hideProgressBar={false}
            newestOnTop={false}
            closeOnClick
            rtl={false}
            pauseOnFocusLoss
            draggable
            pauseOnHover
          />
          <Routers />
        </AuthInitializer>
        {process.env.NODE_ENV === "development" && (
          <ReactQueryDevtools initialIsOpen={false} />
        )}
      </QueryClientProvider>
    </Provider>
  );
};

export default App;
