import { configureStore } from "@reduxjs/toolkit";
import wizardReducer from "../features/wizard/wizardSlice";
import fileUploadReducer from "../features/fileUpload/fileUploadSlice";
import authReducer from "../features/auth/authSlice";

export const store = configureStore({
  reducer: {
    wizard: wizardReducer,
    fileUpload: fileUploadReducer,
    auth: authReducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
