@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: 'icomoon';
  src:  url('./assets/fonts/icomoon.eot?1zvk9b');
  src:  url('./assets/fonts/icomoon.eot?1zvk9b#iefix') format('embedded-opentype'),
    url('./assets/fonts/icomoon.ttf?1zvk9b') format('truetype'),
    url('./assets/fonts/icomoon.woff?1zvk9b') format('woff'),
    url('./assets/fonts/icomoon.svg?1zvk9b#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-Company-Information:before {
  content: "\e900";
}
.icon-Dashboard:before {
  content: "\e901";
}
.icon-Data-Ingestion:before {
  content: "\e902";
}
.icon-Forecasting:before {
  content: "\e903";
}
.icon-Prosessed-Data:before {
  content: "\e904";
}
.icon-Stores:before {
  content: "\e905";
}


.dropdown-menu li a{
  padding: 16px 16px 16px 22px;
}
.dropdown-menu li a:last-child{
  padding: 16px 16px 0px 22px;
}
.menu-active{
  background-color:  var(--color-primary);
}
