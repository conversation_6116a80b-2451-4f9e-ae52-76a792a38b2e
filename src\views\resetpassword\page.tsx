/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import logo from "../../assets/Flashana Logo.png";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { useDispatch, useSelector } from "react-redux";
import type { AppDispatch, RootState } from "../../app/store";
import { setLoading, clearAuthError } from "../../features/auth/authSlice";
import { usePostResetPassword } from "./hook";
import toastNotification from "../../utility/toastService/toastNotification";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp";

const ResetPasswordPage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const { forgotPwdUser } = useSelector((state: RootState) => state.auth);
  const [otpDigits, setOtpDigits] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const onPostSuccess = (data: any) => {
    dispatch(setLoading(false));
    toastNotification.success(data?.message || "Password reset successfully.");
    setTimeout(() => {
      navigate("/");
    }, 2000);
  };

  const onPostError = (error: any) => {
    dispatch(setLoading(false));
    const errorMsg =
      error?.error?.message || "Something went wrong. Please try again.";
    toastNotification.error(errorMsg);
  };

  const { postMutate } = usePostResetPassword(onPostSuccess, onPostError);

  useEffect(() => {
    dispatch(clearAuthError());
  }, [dispatch]);

  const handleSubmit = () => {
    const newErrors: { [key: string]: string } = {};

    if (otpDigits.length !== 6) newErrors.otpDigits = "OTP must be 6 digits.";
    if (!newPassword) newErrors.newPassword = "New password is required.";
    if (!confirmPassword)
      newErrors.confirmPassword = "Please confirm your password.";
    if (newPassword && confirmPassword && newPassword !== confirmPassword) {
      newErrors.confirmPassword = "Passwords do not match.";
    }

    setErrors(newErrors);

    if (Object.keys(newErrors).length > 0) return;

    if (!forgotPwdUser?.email) {
      toastNotification.error("Email missing. Please try again.");
      return;
    }

    dispatch(setLoading(true));

    // Use React Query mutation instead of direct service call
    const dataPayload: any = {
      email: forgotPwdUser?.email || "",
      confirmation_code: otpDigits,
      new_password: newPassword,
    };

    postMutate(dataPayload);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-white px-4">
      <div className="w-full max-w-md text-center space-y-6">
        <img src={logo} alt="Flashana Logo" className="mx-auto w-[160px]" />

        <div>
          <h2 className="text-2xl font-bold text-primary">Forgot Password</h2>
          <p className="text-gray-600 text-sm">
            Please enter your new password.
          </p>
        </div>
        <div className="space-y-6">
          {/* OTP Inputs */}
          <div className="flex flex-col space-x-0">
            <Label
              htmlFor="newPassword"
              className="block text-left text-sm font-medium text-gray-700 mb-1"
            >
              OTP<span className="text-red-500">*</span>
            </Label>
            <InputOTP
              maxLength={6}
              value={otpDigits}
              onChange={(otpDigits: string) => {
                setOtpDigits(otpDigits);
                if (errors.otpDigits)
                  setErrors((prev) => ({ ...prev, otpDigits: "" }));
              }}
            >
              <InputOTPGroup>
                <InputOTPSlot index={0} />
                <InputOTPSlot index={1} />
                <InputOTPSlot index={2} />
                <InputOTPSlot index={3} />
                <InputOTPSlot index={4} />
                <InputOTPSlot index={5} />
              </InputOTPGroup>
            </InputOTP>
            {errors.otpDigits && (
              <p className="text-sm text-red-600 text-left">
                {errors.otpDigits}
              </p>
            )}
          </div>

          {/* New Password */}
          <div className="text-left">
            <Label
              htmlFor="newPassword"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              New Password<span className="text-red-500">*</span>
            </Label>
            <Input
              id="newPassword"
              type="password"
              value={newPassword}
              onChange={(e) => {
                setNewPassword(e.target.value);
                if (errors.newPassword)
                  setErrors((prev) => ({ ...prev, newPassword: "" }));
              }}
              required
            />
            {errors.newPassword && (
              <p className="text-sm text-red-600">{errors.newPassword}</p>
            )}
          </div>

          {/* Confirm Password */}
          <div className="text-left">
            <Label
              htmlFor="confirmPassword"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Confirm Password<span className="text-red-500">*</span>
            </Label>
            <Input
              id="confirmPassword"
              type="password"
              value={confirmPassword}
              onChange={(e) => {
                setConfirmPassword(e.target.value);
                if (errors.confirmPassword)
                  setErrors((prev) => ({ ...prev, confirmPassword: "" }));
              }}
              required
            />
            {errors.confirmPassword && (
              <p className="text-sm text-red-600">{errors.confirmPassword}</p>
            )}
          </div>

          {/* Submit Button */}
          <Button
            type="button"
            onClick={handleSubmit}
            className="w-full bg-[#22423F] text-white py-3 px-4 text-base font-semibold rounded-md"
          >
            Submit
            {/* {isLoading ? "Submitting..." : ""} */}
          </Button>

          {/* Back to Login */}
          <div className="text-sm text-center text-gray-700 mt-4">
            <Link
              to="/login"
              className="flex items-center justify-center hover:underline"
            >
              <span className="mr-1">←</span> Back to login
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResetPasswordPage;
