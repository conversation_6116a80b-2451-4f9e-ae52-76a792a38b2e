/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useEffect } from "react";
import { useNavigate, Link } from "react-router-dom";
import { ArrowLeft } from "lucide-react";
import logo from "../../assets/Flashana Logo.png";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import toastNotification from "../../utility/toastService/toastNotification";
import { useDispatch, useSelector } from "react-redux";
import type { AppDispatch, RootState } from "../../app/store";
import {
  setLoading,
  clearAuthError,
  forgotPasswordFailure,
  forgotPasswordSuccess,
} from "../../features/auth/authSlice";
import { usePostForgotPassword } from "./hook";

const ForgotPasswordPage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const { isLoading, error: authError } = useSelector(
    (state: RootState) => state.auth
  );

  const [email, setEmail] = useState("");

  const onPostSuccess = (data: any) => {
    dispatch(setLoading(false));
    toastNotification.success(data?.message || "OTP sent to your email");

    // Store user data in redux store
    dispatch(forgotPasswordSuccess({ email: email || "" }));

    setTimeout(() => {
      navigate("/reset-password");
    }, 2000);
  };

  const onPostError = (error: any) => {
    dispatch(setLoading(false));
    const errorMsg =
      error?.error?.message || "Something went wrong. Please try again.";
    toastNotification.error(errorMsg);
  };

  const { postMutate } = usePostForgotPassword(onPostSuccess, onPostError);

  useEffect(() => {
    dispatch(clearAuthError());
  }, [dispatch]);

  const handleSubmit = async (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();

    if (!email.trim()) {
      dispatch(forgotPasswordFailure("Email is required."));
      return;
    }

    dispatch(setLoading(true));
    const userDataPayload: any = {
      email: email,
    };
    console.log("Forgot password payload:", userDataPayload);
    postMutate(userDataPayload);
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-white px-4">
      <div className="max-w-md w-full space-y-8 text-center">
        <img src={logo} alt="Flashana Logo" className="mx-auto w-[160px]" />

        <h2 className="text-2xl font-bold text-[#22423F]">Forgot Password</h2>
        <p className="text-gray-600 text-sm">
          Please enter your email to send verification code.
        </p>

        <div className="text-left">
          <Label
            htmlFor="email"
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            Email<span className="text-red-500">*</span>
          </Label>
          <Input
            id="email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
          />
        </div>

        {authError && (
          <p className="text-sm text-red-600 text-center">{authError}</p>
        )}

        <Button
          type="submit"
          onClick={handleSubmit}
          className="w-full bg-[#22423F] text-white py-2 font-semibold"
        >
          {isLoading ? "Sending..." : "Submit"}
        </Button>

        <Link
          to="/login"
          className="flex items-center justify-center text-sm text-gray-700 mt-6 hover:underline"
        >
          <ArrowLeft className="mr-1 w-4 h-4" />
          Back to login
        </Link>
      </div>
    </div>
  );
};

export default ForgotPasswordPage;
