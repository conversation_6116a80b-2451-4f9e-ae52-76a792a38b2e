import * as React from "react";
import { ChevronRight } from "lucide-react";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from "@/components/ui/sidebar";
import Flashanalogo from "../assets/Flashana Logo.png";

// Map menu titles to icons
const menuIcons: Record<string, React.ReactNode> = {
  Dashboard: <span className="icon-Dashboard"></span>,
  Stores: <span className="icon-Stores"></span>,
  "Company Information": <span className="icon-Company-Information"></span>,
  "Data Ingestion": <span className="icon-Data-Ingestion"></span>,
  Forecasting: <span className="icon-Forecasting"></span>,
};
const data = {
  versions: ["1.0.1", "1.1.0-alpha", "2.0.0-beta1"],
  navMain: [
    {
      title: "Dashboard",
      url: "/dashboard",
      isActive: true,
    },
    {
      title: "Stores",
      url: "/create-store",
      isActive: false,
    },
    {
      title: "Company Information",
      url: "#",
      isActive: false,
      items: [
        {
          title: "Company Details",
          url: "/company-details",
          isActive: false,
        },
        {
          title: "Holiday List",
          url: "#",
          isActive: false,
        },
      ],
    },
    {
      title: "Data Ingestion",
      url: "#",
      isActive: false,
      items: [
        {
          title: "File Batches",
          url: "#",
          isActive: false,
        },
        {
          title: "Imported Data",
          url: "#",
          isActive: false,
        },
      ],
    },
    {
      title: "Forecasting",
      url: "#",
      isActive: false,
    },
  ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar {...props}>
      <SidebarHeader>
        <div className="flex items-center justify-center">
          <img src={Flashanalogo} alt="Flashana Logo" className="w-[165px]" />
        </div>
      </SidebarHeader>
      <SidebarContent className="gap-0">
        {/* Render first 2 as simple menu items */}
        <SidebarMenu>
          {data.navMain.slice(0, 2).map((item) => (
            <SidebarMenuItem key={item.title}>
              <SidebarMenuButton asChild isActive={item.isActive}>
                <a href={item.url} className="flex items-center">
                  {menuIcons[item.title]}
                  {item.title}
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
        {/* Render next 2 as collapsible groups */}
        {data.navMain.slice(2, 4).map((item) => (
          <Collapsible
            key={item.title}
            className="group/collapsible dropdown-menu p-3"
          >
            <SidebarGroup>
              <SidebarGroupLabel
                asChild
                className="group/label text-sm text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
              >
                <CollapsibleTrigger>
                  <span className="flex items-center gap-2">
                    {menuIcons[item.title]}
                    {item.title}
                  </span>
                  <ChevronRight className="ml-auto transition-transform group-data-[state=open]/collapsible:rotate-90" />
                </CollapsibleTrigger>
              </SidebarGroupLabel>
              <CollapsibleContent>
                <SidebarGroupContent>
                  <SidebarMenu>
                    {item.items?.map((subitem) => (
                      <SidebarMenuItem key={subitem.title}>
                        <SidebarMenuButton asChild>
                          <a href={subitem.url} className="flex items-center">
                            {menuIcons[subitem.title]}
                            {subitem.title}
                          </a>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                    ))}
                  </SidebarMenu>
                </SidebarGroupContent>
              </CollapsibleContent>
            </SidebarGroup>
          </Collapsible>
        ))}
        <SidebarMenu>
          {data.navMain.slice(4, 5).map((item) => (
            <SidebarMenuItem key={item.title}>
              <SidebarMenuButton asChild>
                <a href={item.url} className="flex items-center">
                  {menuIcons[item.title]}
                  {item.title}
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarContent>
      <SidebarRail />
    </Sidebar>
  );
}
