import { createSlice } from "@reduxjs/toolkit";
import type { PayloadAction } from "@reduxjs/toolkit";

export type PreviewRow = Record<string, string | number | boolean | null>;

interface FileInfo {
  name: string;
  size: number;
  type: string;
  rawFile: File;
}

interface FileUploadState {
  selectedFormat: string;
  fileInfo: FileInfo | null;
  fileData: File | null;
  previewData: PreviewRow[] | null;
  isPreviewOpen: boolean;
}

const initialState: FileUploadState = {
  selectedFormat: "CSV",
  fileInfo: null,
  fileData: null,
  previewData: null,
  isPreviewOpen: false,
};

export const fileUploadSlice = createSlice({
  name: "fileUpload",
  initialState,
  reducers: {
    setSelectedFormat: (state, action: PayloadAction<string>) => {
      state.selectedFormat = action.payload;
    },
    setFileInfo: (state, action: PayloadAction<FileInfo>) => {
      state.fileInfo = action.payload;
      state.fileData = action.payload.rawFile;
    },
    clearFileInfo: (state) => {
      state.fileInfo = null;
      state.fileData = null;
      state.previewData = null;
    },
    setPreviewData: (state, action: PayloadAction<PreviewRow[] | null>) => {
      state.previewData = action.payload;
    },
    openPreview: (state) => {
      state.isPreviewOpen = true;
    },
    closePreview: (state) => {
      state.isPreviewOpen = false;
    },
  },
});

export const {
  setSelectedFormat,
  setFileInfo,
  clearFileInfo,
  setPreviewData,
  openPreview,
  closePreview,
} = fileUploadSlice.actions;
export default fileUploadSlice.reducer;
