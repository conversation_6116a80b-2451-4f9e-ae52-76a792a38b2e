import ReactDOM from "react-dom/client";
import "./App.css";
import App from "../src/App";

import { Amplify } from "aws-amplify";
import { BrowserRouter } from "react-router-dom";

Amplify.configure({
  Auth: {
    Cognito: {
      userPoolId: "us-east-1_bzPZxTad7",
      userPoolClientId: "3kqorl98hmfjiiblabqd61ru9p",
    },
  },
});

ReactDOM.createRoot(document.getElementById("root")!).render(
  // <React.StrictMode>
  //   <Provider store={store}>
  //     <Routers />
  //   </Provider>
  // </React.StrictMode>

  <BrowserRouter>
    <App />
  </BrowserRouter>
);
