import React from "react";

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  variant?: "primary" | "outline" | "grey" | "green";
}

const Button: React.FC<ButtonProps> = ({
  children,
  className = "",
  variant = "primary",
  disabled = false,
  ...props
}) => {
  const baseStyle =
    "py-2 px-4 font-semibold rounded-lg focus:outline-none focus:ring-2 focus:ring-opacity-50 disabled:opacity-50";
  const variants = {
    primary: "bg-blue-500 hover:bg-blue-600 text-white focus:ring-blue-400",
    outline:
      "bg-transparent hover:bg-gray-100 text-blue-700 border border-blue-500 focus:ring-blue-400",
    grey: "bg-gray-400 text-white cursor-not-allowed",
    green: "bg-flashanaGreen hover:bg-blue-500 text-white focus:ring-green-400",
  };
  return (
    <button
      disabled={disabled}
      className={`${baseStyle} ${variants[variant]} ${className}`}
      {...props}
    >
      {children}
    </button>
  );
};
export default Button;
