import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent, CardFooter } from "../../components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { Button } from "@/components/ui/button";

const CompanyDetailsPage = () => {
  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-semibold">
            Company Details
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="companyName">Company Name</Label>
                <Input id="companyName" placeholder="Company Name" />
              </div>
              <div>
                <Label htmlFor="companyType">Company Type</Label>
                <Select>
                  <SelectTrigger id="companyType">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="private">Private</SelectItem>
                    <SelectItem value="public">Public</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="numberOfStores">Number of Stores</Label>
                <Input id="numberOfStores" placeholder="Number of Stores" />
              </div>
              <div>
                <Label htmlFor="websiteUrl">Website URL (Optional)</Label>
                <Input id="websiteUrl" placeholder="Website URL (Optional)" />
              </div>
              <div>
                <Label htmlFor="country">Country</Label>
                <Select>
                  <SelectTrigger id="country">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="india">India</SelectItem>
                    <SelectItem value="usa">USA</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="state">State / Region</Label>
                <Select>
                  <SelectTrigger id="state">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="state1">State 1</SelectItem>
                    <SelectItem value="state2">State 2</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="city">City</Label>
                <Select>
                  <SelectTrigger id="city">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="city1">City 1</SelectItem>
                    <SelectItem value="city2">City 2</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div>
              <Label htmlFor="streetAddress">Street Address</Label>
              <Input id="streetAddress" placeholder="Street Address" />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="zip">ZIP/Postal Code</Label>
                <Input id="zip" placeholder="ZIP/Postal Code" />
              </div>
              <div>
                <Label htmlFor="taxId">
                  Tax ID/ Business Registration Number
                </Label>
                <Input
                  id="taxId"
                  placeholder="Tax ID/ Business Registration Number"
                />
              </div>
            </div>
          </form>
        </CardContent>
        <CardFooter className="justify-end">
          <Button
            type="submit"
            className="bg-primary text-white px-6 py-2 rounded"
          >
            Submit
          </Button>
        </CardFooter>
      </Card>
    </>
  );
};
export default CompanyDetailsPage;
