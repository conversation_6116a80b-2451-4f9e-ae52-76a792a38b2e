/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useEffect, useRef } from "react";
import { Link, useNavigate } from "react-router-dom";
import { ArrowLeft } from "lucide-react";
import { Button } from "../../components/ui/button";
import logo from "../../assets/Flashana Logo.png";
import { usePostOtpVerify, usePostResendOtp } from "./hooks";
import toastNotification from "../../utility/toastService/toastNotification";
import { useDispatch, useSelector } from "react-redux";
import type { AppDispatch, RootState } from "../../app/store";
import {
  setLoading,
  clearAuthError,
  otpVerifyFailure,
} from "../../features/auth/authSlice";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp";

const OTPVerificationPage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const {
    isLoading,
    signupUser,
    error: authError,
  } = useSelector((state: RootState) => state.auth);

  const [otpDigits, setOtpDigits] = useState<string>("");

  const onPostSuccess = (data: any) => {
    dispatch(setLoading(false));
    console.log("Otp verify success:", data);
    toastNotification.success(data?.message || "Email verified");

    setTimeout(() => {
      navigate("/");
    }, 3000);
  };

  const onPostError = (error: any) => {
    dispatch(setLoading(false));
    const errorInfo =
      error?.error?.message || "Something went wrong. Please try again.";
    toastNotification.error(errorInfo);
  };

  const onResendSuccess = (data: any) => {
    dispatch(setLoading(false));
    toastNotification.success(data?.message || "OTP resent successfully.");
  };

  const onResendError = (error: any) => {
    dispatch(setLoading(false));
    const errorInfo =
      error?.error?.message || "Failed to resend OTP. Please try again.";
    toastNotification.error(errorInfo);
  };

  const { postMutate } = usePostOtpVerify(onPostSuccess, onPostError);

  const { postMutate: resendMutate } = usePostResendOtp(
    onResendSuccess,
    onResendError
  );

  useEffect(() => {
    dispatch(clearAuthError());
  }, [dispatch]);

  const handleOtpSubmit = async (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();

    if (otpDigits.length < 6) {
      dispatch(otpVerifyFailure("Please enter the complete 6-digit OTP."));
      return;
    }

    if (!signupUser?.email || !signupUser?.user_id || !signupUser?.tenant_id) {
      toastNotification.error(
        "Signup data missing. Please try signing up again."
      );
      return;
    }

    dispatch(setLoading(true));

    // Use React Query mutation instead of direct service call
    const dataPayload: any = {
      email: signupUser.email,
      user_id: signupUser.user_id,
      tenant_id: signupUser.tenant_id,
      confirmation_code: otpDigits,
    };

    postMutate(dataPayload);
  };

  const handleResendOtp = async (e: React.MouseEvent<HTMLSpanElement>) => {
    e.preventDefault();
    console.log("dushuhsiu");

    if (!signupUser?.email) {
      toastNotification.error("Email missing. Please try signing up again.");
      return;
    }

    dispatch(setLoading(true));

    const dataPayload: any = {
      email: signupUser.email,
    };

    resendMutate(dataPayload);
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-white px-4">
      <div className="max-w-md w-full space-y-8 text-center">
        <img src={logo} alt="Flashana Logo" className="mx-auto w-[160px]" />
        <h2 className="text-2xl font-bold text-[#22423F]">OTP Verification</h2>
        <p className="text-gray-600 text-sm">
          Enter the OTP sent to {signupUser?.email || "your email"}
        </p>

        <div className="flex justify-center gap-2 mt-4">
          <InputOTP
            maxLength={6}
            value={otpDigits}
            onChange={(otpDigits) => setOtpDigits(otpDigits)}
          >
            <InputOTPGroup>
              <InputOTPSlot index={0} />
              <InputOTPSlot index={1} />
              <InputOTPSlot index={2} />
              <InputOTPSlot index={3} />
              <InputOTPSlot index={4} />
              <InputOTPSlot index={5} />
            </InputOTPGroup>
          </InputOTP>
        </div>

        {authError && (
          <p className="text-sm text-red-600 text-center">{authError}</p>
        )}

        <Button
          onClick={handleOtpSubmit}
          className="w-full bg-[#22423F] text-white py-2 font-semibold mt-6"
        >
          {isLoading ? "Verifying OTP..." : "Submit"}
        </Button>

        <p className="text-sm text-gray-600 mt-2">
          Didn’t receive the OTP?{" "}
          <span
            className="text-orange-600 font-medium hover:underline cursor-pointer"
            onClick={handleResendOtp}
          >
            Resend
          </span>
        </p>

        <Link
          to="/signup"
          className="flex items-center justify-center text-sm text-gray-700 mt-6 hover:underline"
        >
          <ArrowLeft className="mr-1 w-4 h-4" />
          Back to Signup
        </Link>
      </div>
    </div>
  );
};

export default OTPVerificationPage;
