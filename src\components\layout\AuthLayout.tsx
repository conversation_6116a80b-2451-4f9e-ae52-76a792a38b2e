import React from "react";
import logo from "../../assets/Flashana Logo.png";

interface AuthLayoutProps {
  children: React.ReactNode;
}

const AuthLayout: React.FC<AuthLayoutProps> = ({ children }) => {
  return (
    <div className="min-h-screen bg-gray-100 flex flex-col justify-center items-center p-4">
      <div className="w-full max-w-md">
        <img
          src={logo}
          alt="Flashana Logo"
          className="mx-auto h-12 w-auto mb-8"
        />
        <div className="bg-white shadow-xl rounded-lg p-8 md:p-10">
          {children}
        </div>
      </div>
    </div>
  );
};

export default AuthLayout;
