import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

const toastNotification = {
  success: (message: string) => {
    toast.success(message);
  },
  error: (message: string) => {
    toast.error(message);
  },
  warning: (message: string) => {
    toast.warning(message);
  },
  info: (message: string) => {
    toast.info(message);
  },
};

export default toastNotification;
