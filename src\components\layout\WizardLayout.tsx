import React from "react";
import { useSelector, useDispatch } from "react-redux";
import { setActiveStep } from "../../features/wizard/wizardSlice";
import type { WizardStep } from "../../features/wizard/wizardSlice";
import type { RootState, AppDispatch } from "../../app/store";
import UploadStep from "../wizardSteps/UploadStep";
import SchemaMapperStep from "../wizardSteps/SchemaMapperStep";
import ValidationStep from "../wizardSteps/ValidationStep";
import ImportStep from "../wizardSteps/ImportStep";

const WizardLayout: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { activeStep, steps } = useSelector((state: RootState) => state.wizard);

  const renderStepContent = () => {
    switch (activeStep) {
      case "Upload":
        return <UploadStep />;
      case "Schema Mapper":
        return <SchemaMapperStep />;
      case "Validation & Cleaning":
        return <ValidationStep />;
      case "Import":
        return <ImportStep />;
      default:
        return <UploadStep />;
    }
  };

  return (
    <div className="min-h-screen flex items-start justify-center py-8 sm:py-12 w-full">
      <div className="relative py-3 w-full px-2 sm:px-8">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-blue-600 shadow-lg transform -skew-y-6 sm:skew-y-0 sm:-rotate-3 sm:rounded-3xl"></div>
        <div className="relative w-full mx-auto py-10 bg-white shadow-lg sm:rounded-3xl sm:p-8 md:p-12">
          <h1 className="text-3xl font-bold mb-8 text-center text-gray-800">
            Data Ingestion Wizard
          </h1>
          <div className="flex border-b mb-6 justify-center">
            {steps.map((step: WizardStep) => (
              <button
                key={step}
                className={`py-2 px-4 mr-1 focus:outline-none ${
                  activeStep === step
                    ? "border-b-2 border-blue-500 text-blue-600 font-semibold"
                    : "text-gray-500 hover:text-gray-700"
                }`}
                onClick={() => dispatch(setActiveStep(step))}
              >
                {step}
              </button>
            ))}
          </div>
          <div>{renderStepContent()}</div>
        </div>
      </div>
    </div>
  );
};

export default WizardLayout;
