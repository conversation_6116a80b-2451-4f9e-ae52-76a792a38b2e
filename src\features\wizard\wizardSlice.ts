import { createSlice } from "@reduxjs/toolkit";
import type { PayloadAction } from "@reduxjs/toolkit";

export type WizardStep =
  | "Upload"
  | "Schema Mapper"
  | "Validation & Cleaning"
  | "Import";

interface WizardState {
  activeStep: WizardStep;
  steps: WizardStep[];
}

const initialState: WizardState = {
  activeStep: "Upload",
  steps: ["Upload", "Schema Mapper", "Validation & Cleaning", "Import"],
};

export const wizardSlice = createSlice({
  name: "wizard",
  initialState,
  reducers: {
    setActiveStep: (state, action: PayloadAction<WizardStep>) => {
      state.activeStep = action.payload;
    },
  },
});

export const { setActiveStep } = wizardSlice.actions;
export default wizardSlice.reducer;
