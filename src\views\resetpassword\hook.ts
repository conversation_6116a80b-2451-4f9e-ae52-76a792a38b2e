/* eslint-disable @typescript-eslint/no-explicit-any */

import { useMutation, useQueryClient } from "@tanstack/react-query";
import useDataService from "../../services/useDataService";
import { RESET_PWD_URL } from "../../constants/urls";

export const usePostResetPassword = (onPostSuccess: any, onPostError: any) => {
  const queryClient = useQueryClient();
  const {
    mutate: postMutate,
  } = useMutation<any, Error>({
    mutationFn: async (data: any) => {
      const result = await useDataService.postService(`${RESET_PWD_URL}`, data);
      return result;
    },
    onSuccess: (data: any) => {
      onPostSuccess(data);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["resetPassword"] });// You can keep this as a string or key array
    },
    onError: (error: any) => {
        onPostError(error);
    },
  });

  return {postMutate };
};
