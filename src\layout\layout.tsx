import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar"
import { AppSidebar } from "@/components/app-sidebar"
import React from "react"
import { SiteHeader } from "@/components/site-header"
import { Outlet } from "react-router-dom" // <-- Import Outlet

export default function Layout() {
  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <SiteHeader />
        <main className="h-screen bg-zinc-100 py-4 px-6">
          <Outlet /> {/* This will render the selected route */}
        </main>
      </SidebarInset>
    </SidebarProvider>
  )
}
