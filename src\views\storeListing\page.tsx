import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { <PERSON>, Pencil, Trash2 } from "lucide-react";
import React, { useState } from "react";

const stores = [
  {
    code: "HTR123",
    updated: "08-02-2025",
    type: "Grocery",
    sku: "100-200",
    country: "Canada",
    status: "Active",
  },
  {
    code: "STR257",
    updated: "12-02-2025",
    type: "Pharmacy",
    sku: "200-300",
    country: "Canada",
    status: "Inactive",
  },
  {
    code: "STR257",
    updated: "10-09-2025",
    type: "Pharmacy",
    sku: "300-400",
    country: "Canada",
    status: "Active",
  },
  {
    code: "HTR123",
    updated: "01-25-2025",
    type: "Grocery",
    sku: "500-600",
    country: "Canada",
    status: "Active",
  },
  {
    code: "STR257",
    updated: "05-18-2025",
    type: "Pharmacy",
    sku: "100-200",
    country: "Canada",
    status: "Active",
  },
  {
    code: "HTR123",
    updated: "08-02-2025",
    type: "Grocery",
    sku: "200-300",
    country: "Canada",
    status: "Inactive",
  },
  {
    code: "STR257",
    updated: "06-02-2025",
    type: "Pharmacy",
    sku: "300-400",
    country: "Canada",
    status: "Inactive",
  },
  {
    code: "HTR123",
    updated: "08-02-2025",
    type: "Grocery",
    sku: "700-800",
    country: "Canada",
    status: "Active",
  },
  {
    code: "STR257",
    updated: "10-02-2025",
    type: "Pharmacy",
    sku: "300-400",
    country: "Canada",
    status: "Active",
  },
];

const StoreListingPage = () => {
  const [search, setSearch] = useState("");

  const filteredStores = stores.filter(
    (store) =>
      store.code.toLowerCase().includes(search.toLowerCase()) ||
      store.type.toLowerCase().includes(search.toLowerCase()) ||
      store.country.toLowerCase().includes(search.toLowerCase())
  );

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-semibold flex items-center gap-2">
          <span className="inline-block border rounded p-1 mr-2" />
          Stores
        </h1>
      </div>
      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-4">
          <h2 className="text-lg font-semibold">Stores Listing</h2>
          <div className="flex gap-2 w-full md:w-auto">
            <Input
              placeholder="Search"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="md:w-[220px]"
            />
            <Button className="bg-[#22423F] text-white font-semibold">
              Create Store
            </Button>
          </div>
        </div>
        <div className="rounded-lg border bg-background">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="font-semibold">
                  Store Code / Name
                </TableHead>
                <TableHead className="font-semibold">Last Updated</TableHead>
                <TableHead className="font-semibold">Store Type</TableHead>
                <TableHead className="font-semibold">SKU Count</TableHead>
                <TableHead className="font-semibold">Country</TableHead>
                <TableHead className="font-semibold text-center">Status</TableHead>
                <TableHead className="font-semibold text-center">Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredStores.map((store, idx) => (
                <TableRow key={idx}>
                  <TableCell>{store.code}</TableCell>
                  <TableCell>{store.updated}</TableCell>
                  <TableCell>{store.type}</TableCell>
                  <TableCell>{store.sku}</TableCell>
                  <TableCell>{store.country}</TableCell>
                  <TableCell className="text-center">
                    <Badge
                      variant={store.status === "Active" ? "default" : "destructive"}
                      className={
                        store.status === "Active"
                          ? "bg-green-100 text-green-700 hover:bg-green-100 text-center"
                          : "bg-red-100 text-red-700 hover:bg-red-100 text-center"
                      }
                    >
                      {store.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2 justify-center">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="hover:bg-gray-100"
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="hover:bg-gray-100"
                      >
                        <Pencil className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="hover:bg-gray-100"
                      >
                        <Trash2 className="w-4 h-4 text-red-500" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
        <div className="flex items-center justify-between mt-4 text-sm text-gray-500">
          <span>Showing 1-10 of 30 rows</span>
          <div className="flex gap-1">
            <Button
              variant="outline"
              size="icon"
              className="px-2 py-1"
            >{"<"}</Button>
            <Button
              variant="default"
              size="icon"
              className="px-3 py-1 bg-[#22423F] text-white"
            >
              1
            </Button>
            <Button
              variant="outline"
              size="icon"
              className="px-2 py-1"
            >
              2
            </Button>
            <Button
              variant="outline"
              size="icon"
              className="px-2 py-1"
            >
              3
            </Button>
            <Button
              variant="outline"
              size="icon"
              className="px-2 py-1"
            >{">"}</Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StoreListingPage;