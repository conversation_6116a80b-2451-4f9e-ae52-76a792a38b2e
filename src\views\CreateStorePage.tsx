import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ooter,
} from "../components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";

const CreateStorePage = () => {
  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-semibold">
            Create a Store
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form className="space-y-8">
            {/* Store Code/Name & Store Type */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="storeCode">
                  Store Code / Name<span className="text-red-500">*</span>
                </Label>
                <Input
                  id="storeCode"
                  placeholder="Enter store code or name"
                  required
                />
              </div>
              <div>
                <Label htmlFor="storeType">
                  Store Type<span className="text-red-500">*</span>
                </Label>
                <Select>
                  <SelectTrigger id="storeType">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="type1">Type 1</SelectItem>
                    <SelectItem value="type2">Type 2</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            {/* Store Color & SKU Count */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="storeColor">Store Color (color selector)</Label>
                <div className="flex items-center gap-2">
                  <input
                    type="color"
                    id="storeColor"
                    defaultValue="#abba30"
                    className="w-8 h-8 border rounded"
                  />
                  <Input value="#abba30" readOnly className="flex-1" />
                </div>
              </div>
              <div>
                <Label htmlFor="skuCount">Approximate SKU Count</Label>
                <Select>
                  <SelectTrigger id="skuCount">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="100">100</SelectItem>
                    <SelectItem value="500">500</SelectItem>
                    <SelectItem value="1000">1000</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            {/* Location Details */}
            <div>
              <div className="font-semibold mb-2">Location Details</div>
              <div className="grid grid-cols-12 gap-6">
                <div className="col-span-12 md:col-span-6">
                  <Label
                    htmlFor="country"
                    className="text-sm font-medium mb-1 block"
                  >
                    Country
                  </Label>
                  <Select>
                    <SelectTrigger id="country" className="w-full">
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="india">India</SelectItem>
                      <SelectItem value="usa">USA</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="col-span-6 md:col-span-3">
                  <Label
                    htmlFor="state"
                    className="text-sm font-medium mb-1 block"
                  >
                    State / Region
                  </Label>
                  <Select>
                    <SelectTrigger id="state" className="w-full">
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="state1">State 1</SelectItem>
                      <SelectItem value="state2">State 2</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="col-span-6 md:col-span-3">
                  <Label
                    htmlFor="city"
                    className="text-sm font-medium mb-1 block"
                  >
                    City
                  </Label>
                  <Select>
                    <SelectTrigger id="city" className="w-full">
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="city1">City 1</SelectItem>
                      <SelectItem value="city2">City 2</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <div>
                  <Label htmlFor="streetAddress">Street Address</Label>
                  <Input id="streetAddress" placeholder="Street Address" />
                </div>
                <div>
                  <Label htmlFor="zip">ZIP/Postal Code</Label>
                  <Input id="zip" placeholder="ZIP/Postal Code" />
                </div>
              </div>
            </div>
            {/* Store Contact Info */}
            <div>
              <div className="font-semibold mb-2">Store Contact Info</div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="contactName">Primary Contact Name</Label>
                  <Input id="contactName" placeholder="Primary Contact Name" />
                </div>
                <div>
                  <Label htmlFor="contactEmail">Contact Email</Label>
                  <Input id="contactEmail" placeholder="Contact Email" />
                </div>
              </div>
            </div>
          </form>
        </CardContent>
        <CardFooter className="justify-end">
          <Button
            type="submit"
            className="bg-primary text-white px-6 py-2 rounded"
          >
            Submit
          </Button>
        </CardFooter>
      </Card>
    </>
  );
};
export default CreateStorePage;
