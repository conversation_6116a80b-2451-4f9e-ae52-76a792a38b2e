import React, { useEffect } from "react";
import { useDispatch } from "react-redux";
// import { getCurrentUser, fetchUserAttributes } from "aws-amplify/auth";
import type { AppDispatch, RootState } from "../../app/store";
import { logoutSuccess, setLoading } from "../../features/auth/authSlice";
import { useSelector } from "react-redux";

interface AuthInitializerProps {
  children: React.ReactNode;
}

const AuthInitializer: React.FC<AuthInitializerProps> = ({ children }) => {
  const dispatch = useDispatch<AppDispatch>();

  const { isAuthenticated, isLoading } = useSelector((state: RootState) => ({
    isAuthenticated: state.auth.isAuthenticated,
    isLoading: state.auth.isLoading,
  }));

  useEffect(() => {
    const initializeAuth = async () => {
      dispatch(setLoading(true));

      try {
        // Check if user is already signed in
        // const cognitoUser = await getCurrentUser();
        // console.log("Current user found:", cognitoUser);

        // // Fetch user attributes
        // const attributes = await fetchUserAttributes();
        // console.log("User attributes:", attributes);

        // // Determine the username to use
        // const appUsername = attributes.email || attributes.preferred_username || cognitoUser.username;

        // Update Redux state with authenticated user
        // dispatch(
        //   loginSuccess({
        //     username: appUsername,
        //     email: attributes.email,
        //   })
        // );

        console.log(
          "User authenticated successfully[AuthInitializer]:",
          isAuthenticated,
          isLoading
        );
      } catch (error) {
        // User is not signed in or there was an error
        console.log("No authenticated user found:", error);
        dispatch(logoutSuccess());
      } finally {
        dispatch(setLoading(false));
      }
    };

    initializeAuth();
  }, [dispatch]);

  return <>{children}</>;
};

export default AuthInitializer;
