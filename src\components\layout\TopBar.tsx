import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { signOut } from "aws-amplify/auth";
import type { AppDispatch, RootState } from "../../app/store";
import { FaBell, FaUserCircle, FaSignOutAlt } from "react-icons/fa";
import { logoutSuccess } from "../../features/auth/authSlice";

const TopBar: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const user = useSelector((state: RootState) => state.auth.user);

  const handleLogout = async () => {
    try {
      await signOut();
      dispatch(logoutSuccess());
      navigate("/");
    } catch (error) {
      console.error("Error signing out: ", error);
    }
  };

  return (
    <div className="bg-white shadow-md h-16 flex items-center justify-end px-6">
      {user && (
        <span className="text-gray-600 mr-4">Hello, {user.username}</span>
      )}
      <div className="w-10 h-10 flex items-center justify-center rounded-full hover:bg-gray-200 cursor-pointer mr-2">
        <FaBell size={20} />
      </div>
      <div className="w-10 h-10 flex items-center justify-center rounded-full hover:bg-gray-200 cursor-pointer mr-2">
        <FaUserCircle size={20} />
      </div>
      <button
        onClick={handleLogout}
        className="w-10 h-10 flex items-center justify-center rounded-full hover:bg-gray-200 cursor-pointer"
        aria-label="Sign Out"
        title="Sign Out"
      >
        <FaSignOutAlt size={20} />
      </button>
    </div>
  );
};
export default TopBar;
