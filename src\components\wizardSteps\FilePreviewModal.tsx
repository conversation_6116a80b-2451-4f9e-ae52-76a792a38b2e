import React from "react";
import { useSelector, useDispatch } from "react-redux";
import { closePreview } from "../../features/fileUpload/fileUploadSlice";
import type { PreviewRow } from "../../features/fileUpload/fileUploadSlice";
import type { RootState, AppDispatch } from "../../app/store";

const FilePreviewModal: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { isPreviewOpen, previewData } = useSelector(
    (state: RootState) => state.fileUpload
  );

  if (!isPreviewOpen || !previewData) return null;

  const headers =
    previewData.length > 0 ? Object.keys(previewData[0] || {}) : [];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white p-6 rounded-lg shadow-xl max-w-3xl w-full max-h-[80vh] overflow-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">
            File Preview (First 10 data rows)
          </h2>
          <button
            onClick={() => dispatch(closePreview())}
            className="bg-white text-gray-500 hover:text-gray-700 text-2xl focus:outline-none focus:ring-0"
          >
            ×
          </button>
        </div>
        {previewData.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 text-sm">
              <thead className="bg-gray-50">
                <tr>
                  {headers.map((header) => (
                    <th
                      key={header}
                      scope="col"
                      className="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider"
                    >
                      {header}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {previewData.map((row: PreviewRow, rowIndex: number) => (
                  <tr key={rowIndex}>
                    {headers.map((header) => (
                      <td key={header} className="px-3 py-2 whitespace-nowrap">
                        {String(row[header] !== undefined ? row[header] : "")}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <p>No data to preview or file might be empty after parsing.</p>
        )}
      </div>
    </div>
  );
};
export default FilePreviewModal;
