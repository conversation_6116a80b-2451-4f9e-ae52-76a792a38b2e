import { Routes, Route, Navigate } from "react-router-dom";
import ProtectedRoutes from "./ProtectedRoutes";
import PublicRoutes from "./PublicRoutes";
import LoginPage from "../views/login/Page";
import DashboardPage from "../views/dashboard/Page";
import CompanyDetailsPage from "../views/companydetails/Page";
import CreateStorePage from "../views/CreateStorePage";
import OTPVerificationPage from "@/views/otpverifiction/Page";
import ForgotPasswordPage from "@/views/forgotpassword/page";
import Layout from "../layout/layout";
import SignUpPage from "@/views/signup/Page";
import ResetPasswordPage from "@/views/resetpassword/page";

function Routers() {
  return (
    <Routes>
      {/* Public Routes */}
      <Route element={<PublicRoutes />}>
        <Route path="/" element={<LoginPage />} />
        <Route path="/signup" element={<SignUpPage />} />
        <Route path="/otp-verification" element={<OTPVerificationPage />} />
        <Route path="/forgot-password" element={<ForgotPasswordPage />} />
        <Route path="/reset-password" element={<ResetPasswordPage />} />
      </Route>

      {/* Protected Routes */}
      <Route element={<ProtectedRoutes />}>
        <Route element={<Layout />}>
          <Route path="/dashboard" element={<DashboardPage />} />
          <Route path="/company-details" element={<CompanyDetailsPage />} />
          <Route path="/create-store" element={<CreateStorePage />} />
          {/* Add more protected routes here as needed */}
        </Route>
      </Route>

      {/* Catch-all route for 404 */}
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
}

export default Routers;
