import React, { useCallback } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useDropzone } from "react-dropzone";
import type { Accept } from "react-dropzone";
import Papa from "papaparse";
import * as XLSX from "xlsx";
import {
  setSelectedFormat,
  setFileInfo,
  clearFileInfo,
  setPreviewData,
  openPreview,
} from "../../features/fileUpload/fileUploadSlice";
import type { PreviewRow } from "../../features/fileUpload/fileUploadSlice";
import type { RootState, AppDispatch } from "../../app/store";
import Button from "../common/Button";
import RadioButtonGroup from "../common/RadioButtonGroup";
import FilePreviewModal from "./FilePreviewModal";

const UploadStep: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { selectedFormat, fileInfo, fileData } = useSelector(
    (state: RootState) => state.fileUpload
  );

  const formats: string[] = [
    "XML",
    "Excel",
    "CSV",
    "JSON",
    "PostgreSQL Backup",
    "MySQL Backup",
    "MSSQL Backup",
    "REST API",
  ];

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      if (acceptedFiles && acceptedFiles.length > 0) {
        const file = acceptedFiles[0];
        dispatch(
          setFileInfo({
            name: file.name,
            size: file.size,
            type: file.type,
            rawFile: file,
          })
        );
      }
    },
    [dispatch]
  );

  const getAcceptConfig = (): Accept => {
    switch (selectedFormat) {
      case "CSV":
        return { "text/csv": [".csv"] };
      case "Excel":
        return {
          "application/vnd.ms-excel": [".xls"],
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [
            ".xlsx",
          ],
        };
      default:
        return {};
    }
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: getAcceptConfig(),
    multiple: false,
  });

  const handleFormatChange = (format: string) => {
    dispatch(setSelectedFormat(format));
    if (fileInfo) {
      dispatch(clearFileInfo());
    }
  };

  const handleUploadClick = () => {
    if (!fileInfo) {
      alert("Please select a file first!");
      return;
    }
    console.log(
      "Upload clicked. Format:",
      selectedFormat,
      "File Info:",
      fileInfo.name
    );
    alert(
      `Simulating upload for ${fileInfo.name} with format ${selectedFormat}`
    );
  };

  const handlePreview = () => {
    if (!fileData) {
      alert("No file selected for preview.");
      return;
    }
    const reader = new FileReader();
    reader.onload = (event: ProgressEvent<FileReader>) => {
      if (!event.target || !event.target.result) {
        alert("Error reading file content.");
        return;
      }
      const fileContent = event.target.result;
      try {
        let parsedResult: PreviewRow[] = [];
        if (
          selectedFormat === "CSV" &&
          (fileData.type.includes("csv") || fileData.name.endsWith(".csv"))
        ) {
          const result = Papa.parse<PreviewRow>(fileContent as string, {
            header: true,
            preview: 10,
            skipEmptyLines: true,
          });
          parsedResult = result.data;
        } else if (
          selectedFormat === "Excel" &&
          (fileData.type.includes("excel") ||
            fileData.type.includes("spreadsheetml") ||
            fileData.name.endsWith(".xls") ||
            fileData.name.endsWith(".xlsx"))
        ) {
          const workbook = XLSX.read(fileContent, { type: "binary" });
          const sheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[sheetName];
          const jsonDataRaw: any[][] = XLSX.utils.sheet_to_json(worksheet, {
            header: 1,
            defval: "",
          });

          if (jsonDataRaw.length > 0) {
            const headers: string[] = jsonDataRaw[0].map(String);
            const dataRows = jsonDataRaw.slice(1, 11);
            parsedResult = dataRows.map((rowArray: any[]) => {
              const rowObject: PreviewRow = {};
              headers.forEach((header, index) => {
                rowObject[header] =
                  rowArray[index] !== undefined ? String(rowArray[index]) : "";
              });
              return rowObject;
            });
          }
        } else {
          alert(
            `Preview for ${selectedFormat} is not yet supported or file type/name mismatch.`
          );
          return;
        }
        dispatch(setPreviewData(parsedResult));
        dispatch(openPreview());
      } catch (error) {
        console.error("Error parsing file:", error);
        alert("Error parsing file for preview.");
      }
    };
    reader.onerror = () => {
      console.error("Error reading file");
      alert("Error reading file.");
    };

    if (selectedFormat === "Excel") {
      reader.readAsBinaryString(fileData);
    } else {
      reader.readAsText(fileData);
    }
  };

  return (
    <div className="space-y-6">
      <p className="text-gray-700 text-base">
        Please select the format of data, you would like to ingest in Flashana:
      </p>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-x-8 gap-y-6 items-start">
        <div className="md:col-span-1">
          <RadioButtonGroup
            options={formats}
            selectedValue={selectedFormat}
            onChange={handleFormatChange}
            name="fileFormat"
          />
        </div>

        <div className="md:col-span-2 space-y-6">
          <div
            {...getRootProps()}
            className={`border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer 
                        flex flex-col justify-center items-center min-h-[250px] hover:border-blue-500
                        ${
                          isDragActive
                            ? "border-blue-500 bg-blue-50"
                            : "bg-gray-50"
                        }`}
          >
            <input {...getInputProps()} />
            <p className="text-gray-500 text-lg">
              {isDragActive
                ? "Drop the files here ..."
                : `Drop your ${selectedFormat} files here to upload`}
            </p>
            <p className="text-sm text-gray-400 mt-1">
              or click to select files
            </p>
          </div>

          {fileInfo && (
            <div className="p-4 border border-gray-200 rounded-lg bg-white shadow-sm">
              <h3 className="font-semibold text-gray-800 mb-1">
                Selected File:
              </h3>
              <p className="text-sm text-gray-600 truncate">
                Name: {fileInfo.name}
              </p>
              <p className="text-sm text-gray-600">
                Size: {(fileInfo.size / 1024).toFixed(2)} KB
              </p>
              <div className="mt-3">
                <Button
                  onClick={handlePreview}
                  className="mr-3 text-sm py-1 px-3"
                >
                  Preview
                </Button>
                <Button
                  onClick={() => dispatch(clearFileInfo())}
                  variant="outline"
                  className="text-sm py-1 px-3"
                >
                  Clear File
                </Button>
              </div>
            </div>
          )}

          <Button
            onClick={handleUploadClick}
            className="w-full text-base py-2.5 bg-blue-600 hover:bg-blue-700 text-white font-semibold"
          >
            Upload
          </Button>
        </div>
      </div>
      <FilePreviewModal />
    </div>
  );
};
export default UploadStep;
